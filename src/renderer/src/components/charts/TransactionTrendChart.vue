<template>
  <div class="w-full h-full">
    <!-- 图表标题和控制器 -->
    <div class="flex items-center justify-between mb-4">
      <div>
        <h3 class="text-lg font-semibold">{{ title }}</h3>
        <p class="text-sm text-muted-foreground">{{ subtitle }}</p>
      </div>
      <div class="flex items-center space-x-2">
        <!-- 时间范围选择 -->
        <select
          v-model="selectedTimeRange"
          class="px-3 py-1.5 text-xs border rounded-md bg-background hover:bg-muted/50 transition-colors focus:outline-none focus:ring-2 focus:ring-primary/20"
          @change="handleTimeRangeChange"
        >
          <option value="1h">1小时</option>
          <option value="6h">6小时</option>
          <option value="24h">24小时</option>
          <option value="7d">7天</option>
        </select>

        <!-- 图表类型切换 -->
        <select
          v-model="chartType"
          class="px-3 py-1.5 text-xs border rounded-md bg-background hover:bg-muted/50 transition-colors focus:outline-none focus:ring-2 focus:ring-primary/20"
          @change="handleChartTypeChange"
        >
          <option value="line">折线图</option>
          <option value="area">面积图</option>
          <option value="bar">柱状图</option>
        </select>

        <!-- 刷新按钮 -->
        <Button variant="ghost" size="sm" :disabled="isLoading" @click="refreshData">
          <RefreshCw :class="['w-3 h-3', isLoading ? 'animate-spin' : '']" />
        </Button>
      </div>
    </div>

    <!-- 图表容器 -->
    <div
      ref="chartContainer"
      class="w-full bg-gradient-to-br from-slate-50/80 to-blue-50/80 dark:from-gray-900/80 dark:to-slate-800/80 rounded-xl border shadow-sm"
      :style="{ height: chartHeight }"
    >
      <!-- 加载状态 -->
      <div v-if="isLoading" class="flex items-center justify-center h-full">
        <div class="flex flex-col items-center space-y-3">
          <RefreshCw class="w-8 h-8 animate-spin text-primary" />
          <p class="text-sm text-muted-foreground">正在加载交易数据...</p>
        </div>
      </div>

      <!-- ECharts图表 -->
      <div v-else class="h-full p-4">
        <v-chart
          ref="echartsRef"
          class="w-full h-full"
          :option="chartOption"
          :autoresize="true"
          @click="handleChartClick"
        />
      </div>
    </div>

    <!-- 数据统计卡片 -->
    <div class="grid grid-cols-4 gap-3 mt-4">
      <div class="bg-gradient-to-r from-blue-500/10 to-blue-600/10 rounded-lg p-3 border border-blue-200/50">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-xs text-blue-600 font-medium">总交易量</p>
            <p class="text-lg font-bold text-blue-700">{{ formatNumber(totalTransactions) }}</p>
          </div>
          <div class="w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center">
            <TrendingUp class="w-4 h-4 text-blue-600" />
          </div>
        </div>
      </div>

      <div class="bg-gradient-to-r from-green-500/10 to-green-600/10 rounded-lg p-3 border border-green-200/50">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-xs text-green-600 font-medium">成功率</p>
            <p class="text-lg font-bold text-green-700">{{ successRate }}%</p>
          </div>
          <div class="w-8 h-8 bg-green-500/20 rounded-full flex items-center justify-center">
            <CheckCircle class="w-4 h-4 text-green-600" />
          </div>
        </div>
      </div>

      <div class="bg-gradient-to-r from-orange-500/10 to-orange-600/10 rounded-lg p-3 border border-orange-200/50">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-xs text-orange-600 font-medium">异常率</p>
            <p class="text-lg font-bold text-orange-700">{{ errorRate }}%</p>
          </div>
          <div class="w-8 h-8 bg-orange-500/20 rounded-full flex items-center justify-center">
            <AlertTriangle class="w-4 h-4 text-orange-600" />
          </div>
        </div>
      </div>

      <div class="bg-gradient-to-r from-purple-500/10 to-purple-600/10 rounded-lg p-3 border border-purple-200/50">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-xs text-purple-600 font-medium">平均金额</p>
            <p class="text-lg font-bold text-purple-700">{{ formatCurrency(averageAmount) }}</p>
          </div>
          <div class="w-8 h-8 bg-purple-500/20 rounded-full flex items-center justify-center">
            <DollarSign class="w-4 h-4 text-purple-600" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { RefreshCw } from 'lucide-vue-next'
import { Button } from '@/components/ui/button'

/**
 * 组件属性定义
 */
interface Props {
  title?: string
  subtitle?: string
  chartHeight?: string
  autoRefresh?: boolean
  refreshInterval?: number
}

const props = withDefaults(defineProps<Props>(), {
  title: '交易趋势分析',
  subtitle: '实时交易数据监控',
  chartHeight: '300px',
  autoRefresh: true,
  refreshInterval: 30000 // 30秒
})

/**
 * 响应式数据
 */
const selectedTimeRange = ref('24h')
const isLoading = ref(false)
const chartContainer = ref<HTMLElement | null>(null)

// 模拟数据
const totalTransactions = ref(12847)
const successRate = ref(98.5)
const errorRate = ref(1.5)

/**
 * 计算属性 - 生成趋势线坐标点
 */
const transactionTrendPoints = computed(() => {
  const points = []
  for (let i = 0; i <= 10; i++) {
    const x = (i / 10) * 380 + 10
    const y = 100 - (Math.sin(i * 0.5) * 20 + Math.random() * 10 + 30)
    points.push(`${x},${y}`)
  }
  return points.join(' ')
})

const successRatePoints = computed(() => {
  const points = []
  for (let i = 0; i <= 10; i++) {
    const x = (i / 10) * 380 + 10
    const y = 100 - (95 + Math.random() * 5)
    points.push(`${x},${y}`)
  }
  return points.join(' ')
})

const errorRatePoints = computed(() => {
  const points = []
  for (let i = 0; i <= 10; i++) {
    const x = (i / 10) * 380 + 10
    const y = 100 - (Math.random() * 10 + 5)
    points.push(`${x},${y}`)
  }
  return points.join(' ')
})

/**
 * 工具函数
 */
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

/**
 * 事件处理函数
 */
const handleTimeRangeChange = () => {
  console.log('时间范围变更:', selectedTimeRange.value)
  refreshData()
}

const refreshData = async () => {
  isLoading.value = true
  console.log('刷新交易趋势数据...')

  // 模拟数据加载
  setTimeout(() => {
    // 更新模拟数据
    totalTransactions.value = Math.floor(Math.random() * 5000) + 10000
    successRate.value = Math.floor(Math.random() * 5) + 95
    errorRate.value = 100 - successRate.value

    isLoading.value = false
    console.log('交易趋势数据刷新完成')
  }, 1000)
}

/**
 * 自动刷新定时器
 */
let autoRefreshTimer: NodeJS.Timeout

/**
 * 生命周期钩子
 */
onMounted(() => {
  console.log('交易趋势图表组件已挂载')

  // 启动自动刷新
  if (props.autoRefresh) {
    autoRefreshTimer = setInterval(() => {
      refreshData()
    }, props.refreshInterval)
  }

  // 初始化数据
  refreshData()
})

onUnmounted(() => {
  console.log('交易趋势图表组件已卸载')

  // 清理定时器
  if (autoRefreshTimer) {
    clearInterval(autoRefreshTimer)
  }
})
</script>

<style scoped>
/* 简化样式，避免Tailwind冲突 */
.chart-container {
  width: 100%;
  transition: all 0.3s ease;
}

.chart-container:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
  .grid-cols-3 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
    gap: 0.5rem;
  }
}
</style>
