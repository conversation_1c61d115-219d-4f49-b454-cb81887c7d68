/**
 * API配置和通用方法
 * 银行告警监控系统 - API基础配置
 */

import type { ApiResponse } from './types'

// API基础配置
export const API_CONFIG = {
  // 后端服务器地址 - 根据你的实际后端地址修改
  BASE_URL: process.env.NODE_ENV === 'development' 
    ? 'http://localhost:3000/api'  // 开发环境
    : 'http://your-production-server.com/api', // 生产环境
  
  // 请求超时时间（毫秒）
  TIMEOUT: 10000,
  
  // 默认请求头
  DEFAULT_HEADERS: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  }
}

// HTTP状态码
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500,
} as const

// API错误类
export class ApiError extends Error {
  constructor(
    public code: number,
    public message: string,
    public data?: any
  ) {
    super(message)
    this.name = 'ApiError'
  }
}

// 请求拦截器 - 添加认证token等
const addAuthHeaders = (headers: Record<string, string> = {}) => {
  // 从localStorage或其他地方获取token
  const token = localStorage.getItem('auth_token')
  if (token) {
    headers['Authorization'] = `Bearer ${token}`
  }
  return headers
}

// 通用请求方法
export const request = async <T = any>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> => {
  const url = `${API_CONFIG.BASE_URL}${endpoint}`
  
  // 合并默认配置
  const config: RequestInit = {
    timeout: API_CONFIG.TIMEOUT,
    headers: {
      ...API_CONFIG.DEFAULT_HEADERS,
      ...addAuthHeaders(),
      ...options.headers,
    },
    ...options,
  }

  try {
    console.log(`🚀 API请求: ${config.method || 'GET'} ${url}`)
    
    const response = await fetch(url, config)
    
    // 检查HTTP状态
    if (!response.ok) {
      throw new ApiError(
        response.status,
        `HTTP Error: ${response.status} ${response.statusText}`
      )
    }

    const result: ApiResponse<T> = await response.json()
    
    // 检查业务状态码
    if (result.code !== HTTP_STATUS.OK) {
      throw new ApiError(result.code, result.message, result.data)
    }

    console.log(`✅ API响应成功: ${endpoint}`, result.data)
    return result.data
    
  } catch (error) {
    console.error(`❌ API请求失败: ${endpoint}`, error)
    
    if (error instanceof ApiError) {
      throw error
    }
    
    // 网络错误或其他错误
    throw new ApiError(
      HTTP_STATUS.INTERNAL_SERVER_ERROR,
      error instanceof Error ? error.message : '网络请求失败'
    )
  }
}

// GET请求
export const get = <T = any>(endpoint: string, params?: Record<string, any>): Promise<T> => {
  const url = new URL(`${API_CONFIG.BASE_URL}${endpoint}`)
  
  // 添加查询参数
  if (params) {
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        url.searchParams.append(key, String(value))
      }
    })
  }
  
  return request<T>(url.pathname + url.search, {
    method: 'GET',
  })
}

// POST请求
export const post = <T = any>(endpoint: string, data?: any): Promise<T> => {
  return request<T>(endpoint, {
    method: 'POST',
    body: data ? JSON.stringify(data) : undefined,
  })
}

// PUT请求
export const put = <T = any>(endpoint: string, data?: any): Promise<T> => {
  return request<T>(endpoint, {
    method: 'PUT',
    body: data ? JSON.stringify(data) : undefined,
  })
}

// DELETE请求
export const del = <T = any>(endpoint: string): Promise<T> => {
  return request<T>(endpoint, {
    method: 'DELETE',
  })
}

// 错误处理工具函数
export const handleApiError = (error: unknown): string => {
  if (error instanceof ApiError) {
    return error.message
  }
  
  if (error instanceof Error) {
    return error.message
  }
  
  return '未知错误'
}

// 重试机制
export const retryRequest = async <T>(
  requestFn: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> => {
  let lastError: Error
  
  for (let i = 0; i <= maxRetries; i++) {
    try {
      return await requestFn()
    } catch (error) {
      lastError = error instanceof Error ? error : new Error('Unknown error')
      
      if (i === maxRetries) {
        break
      }
      
      console.warn(`请求失败，${delay}ms后重试 (${i + 1}/${maxRetries})`, error)
      await new Promise(resolve => setTimeout(resolve, delay))
      delay *= 2 // 指数退避
    }
  }
  
  throw lastError!
}
