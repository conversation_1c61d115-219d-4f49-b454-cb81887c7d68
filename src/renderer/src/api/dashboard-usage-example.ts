/**
 * Dashboard API使用示例
 * 展示如何在Vue组件中使用Dashboard API
 */

import { ref, onMounted, onUnmounted } from 'vue'
import {
  getDashboardData,
  getKeyMetrics,
  getRealtimeAlerts,
  getRegionStats,
  getTransactionData,
  getSystemStatus,
  refreshAllData
} from './dashboard'
import type {
  KeyMetric,
  AlertItem,
  RegionStat,
  TransactionItem,
  SystemStatus
} from './types'

/**
 * Dashboard数据管理 Composable
 * 在Dashboard.vue中可以这样使用
 */
export function useDashboardData() {
  // 响应式数据
  const keyMetrics = ref<KeyMetric[]>([])
  const realtimeAlerts = ref<AlertItem[]>([])
  const regionStats = ref<RegionStat[]>([])
  const transactionData = ref<TransactionItem[]>([])
  const systemStatus = ref<SystemStatus | null>(null)
  
  // 加载状态
  const isLoading = ref(false)
  const isRefreshing = ref(false)
  const error = ref<string | null>(null)

  /**
   * 加载所有数据
   */
  const loadAllData = async () => {
    if (isLoading.value) return
    
    isLoading.value = true
    error.value = null
    
    try {
      // 方式1: 一次性获取所有数据（推荐）
      const dashboardData = await getDashboardData()
      
      keyMetrics.value = dashboardData.keyMetrics
      realtimeAlerts.value = dashboardData.realtimeAlerts
      regionStats.value = dashboardData.regionStats
      transactionData.value = dashboardData.transactionData
      systemStatus.value = dashboardData.systemStatus
      
      console.log('✅ 仪表盘数据加载完成')
      
    } catch (err) {
      console.error('❌ 加载仪表盘数据失败:', err)
      error.value = err instanceof Error ? err.message : '加载数据失败'
      
      // 如果一次性获取失败，尝试分别获取
      await loadDataSeparately()
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 分别加载各模块数据（备用方案）
   */
  const loadDataSeparately = async () => {
    try {
      // 并行请求各个接口
      const [
        metricsData,
        alertsData,
        regionsData,
        transactionsData,
        statusData
      ] = await Promise.allSettled([
        getKeyMetrics(),
        getRealtimeAlerts({ page: 1, pageSize: 10 }),
        getRegionStats(),
        getTransactionData({ page: 1, pageSize: 20 }),
        getSystemStatus()
      ])

      // 处理各个请求结果
      if (metricsData.status === 'fulfilled') {
        keyMetrics.value = metricsData.value
      }
      
      if (alertsData.status === 'fulfilled') {
        realtimeAlerts.value = alertsData.value
      }
      
      if (regionsData.status === 'fulfilled') {
        regionStats.value = regionsData.value
      }
      
      if (transactionsData.status === 'fulfilled') {
        transactionData.value = transactionsData.value
      }
      
      if (statusData.status === 'fulfilled') {
        systemStatus.value = statusData.value
      }

      console.log('✅ 分别加载数据完成')
      
    } catch (err) {
      console.error('❌ 分别加载数据失败:', err)
      error.value = err instanceof Error ? err.message : '加载数据失败'
    }
  }

  /**
   * 刷新数据
   */
  const refreshData = async () => {
    if (isRefreshing.value) return
    
    isRefreshing.value = true
    
    try {
      // 触发后端刷新
      await refreshAllData()
      
      // 重新加载数据
      await loadAllData()
      
      console.log('✅ 数据刷新完成')
      
    } catch (err) {
      console.error('❌ 刷新数据失败:', err)
      error.value = err instanceof Error ? err.message : '刷新数据失败'
    } finally {
      isRefreshing.value = false
    }
  }

  /**
   * 只刷新系统状态（高频更新）
   */
  const refreshSystemStatus = async () => {
    try {
      const status = await getSystemStatus()
      systemStatus.value = status
    } catch (err) {
      console.error('❌ 刷新系统状态失败:', err)
    }
  }

  /**
   * 自动刷新定时器
   */
  let refreshInterval: NodeJS.Timeout | null = null
  
  const startAutoRefresh = (intervalMs: number = 30000) => {
    if (refreshInterval) {
      clearInterval(refreshInterval)
    }
    
    refreshInterval = setInterval(() => {
      if (!isRefreshing.value) {
        refreshSystemStatus() // 只刷新系统状态，减少服务器压力
      }
    }, intervalMs)
    
    console.log(`🔄 自动刷新已启动，间隔: ${intervalMs}ms`)
  }
  
  const stopAutoRefresh = () => {
    if (refreshInterval) {
      clearInterval(refreshInterval)
      refreshInterval = null
      console.log('⏹️ 自动刷新已停止')
    }
  }

  return {
    // 数据
    keyMetrics,
    realtimeAlerts,
    regionStats,
    transactionData,
    systemStatus,
    
    // 状态
    isLoading,
    isRefreshing,
    error,
    
    // 方法
    loadAllData,
    refreshData,
    refreshSystemStatus,
    startAutoRefresh,
    stopAutoRefresh
  }
}

/**
 * 在Dashboard.vue中的使用示例：
 * 
 * <script setup lang="ts">
 * import { onMounted, onUnmounted } from 'vue'
 * import { useDashboardData } from '@/api/dashboard-usage-example'
 * 
 * const {
 *   keyMetrics,
 *   realtimeAlerts,
 *   regionStats,
 *   transactionData,
 *   systemStatus,
 *   isLoading,
 *   isRefreshing,
 *   error,
 *   loadAllData,
 *   refreshData,
 *   startAutoRefresh,
 *   stopAutoRefresh
 * } = useDashboardData()
 * 
 * onMounted(async () => {
 *   await loadAllData()
 *   startAutoRefresh(30000) // 30秒自动刷新
 * })
 * 
 * onUnmounted(() => {
 *   stopAutoRefresh()
 * })
 * </script>
 */
