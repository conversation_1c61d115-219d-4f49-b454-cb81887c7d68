/**
 * Dashboard相关的API请求
 * 银行告警监控系统 - 仪表盘数据接口
 */

import { get, post, handleApiError, retryRequest } from './index'
import type {
  DashboardData,
  KeyMetric,
  AlertItem,
  RegionStat,
  TransactionItem,
  SystemStatus,
  AlertQueryParams,
  TransactionQueryParams,
  StatsQueryParams,
  PaginatedResponse
} from './types'

/**
 * Dashboard API类
 * 包含所有仪表盘相关的API请求方法
 */
export class DashboardAPI {
  
  /**
   * 获取仪表盘汇总数据
   * 一次性获取所有仪表盘需要的数据
   */
  static async getDashboardData(): Promise<DashboardData> {
    try {
      return await retryRequest(() => get<DashboardData>('/dashboard/summary'))
    } catch (error) {
      console.error('获取仪表盘数据失败:', error)
      throw new Error(`获取仪表盘数据失败: ${handleApiError(error)}`)
    }
  }

  /**
   * 获取关键指标数据
   * 包括今日告警总数、高风险交易、监控账户数、系统健康度
   */
  static async getKeyMetrics(): Promise<KeyMetric[]> {
    try {
      return await get<KeyMetric[]>('/dashboard/metrics')
    } catch (error) {
      console.error('获取关键指标失败:', error)
      throw new Error(`获取关键指标失败: ${handleApiError(error)}`)
    }
  }

  /**
   * 获取实时告警数据
   * @param params 查询参数
   */
  static async getRealtimeAlerts(params?: AlertQueryParams): Promise<AlertItem[]> {
    try {
      const response = await get<PaginatedResponse<AlertItem>>('/dashboard/alerts', params)
      return response.items
    } catch (error) {
      console.error('获取实时告警失败:', error)
      throw new Error(`获取实时告警失败: ${handleApiError(error)}`)
    }
  }

  /**
   * 获取地域分布统计
   * @param params 统计查询参数
   */
  static async getRegionStats(params?: StatsQueryParams): Promise<RegionStat[]> {
    try {
      return await get<RegionStat[]>('/dashboard/region-stats', params)
    } catch (error) {
      console.error('获取地域分布统计失败:', error)
      throw new Error(`获取地域分布统计失败: ${handleApiError(error)}`)
    }
  }

  /**
   * 获取交易监控数据
   * @param params 查询参数
   */
  static async getTransactionData(params?: TransactionQueryParams): Promise<TransactionItem[]> {
    try {
      const response = await get<PaginatedResponse<TransactionItem>>('/dashboard/transactions', params)
      return response.items
    } catch (error) {
      console.error('获取交易监控数据失败:', error)
      throw new Error(`获取交易监控数据失败: ${handleApiError(error)}`)
    }
  }

  /**
   * 获取系统状态监控数据
   */
  static async getSystemStatus(): Promise<SystemStatus> {
    try {
      return await retryRequest(() => get<SystemStatus>('/dashboard/system-status'))
    } catch (error) {
      console.error('获取系统状态失败:', error)
      throw new Error(`获取系统状态失败: ${handleApiError(error)}`)
    }
  }

  /**
   * 刷新所有数据
   * 触发后端重新计算和缓存数据
   */
  static async refreshAllData(): Promise<void> {
    try {
      await post('/dashboard/refresh')
    } catch (error) {
      console.error('刷新数据失败:', error)
      throw new Error(`刷新数据失败: ${handleApiError(error)}`)
    }
  }

  /**
   * 获取告警详情
   * @param alertId 告警ID
   */
  static async getAlertDetail(alertId: string): Promise<AlertItem> {
    try {
      return await get<AlertItem>(`/dashboard/alerts/${alertId}`)
    } catch (error) {
      console.error('获取告警详情失败:', error)
      throw new Error(`获取告警详情失败: ${handleApiError(error)}`)
    }
  }

  /**
   * 获取交易详情
   * @param transactionId 交易ID
   */
  static async getTransactionDetail(transactionId: string): Promise<TransactionItem> {
    try {
      return await get<TransactionItem>(`/dashboard/transactions/${transactionId}`)
    } catch (error) {
      console.error('获取交易详情失败:', error)
      throw new Error(`获取交易详情失败: ${handleApiError(error)}`)
    }
  }

  /**
   * 标记告警为已处理
   * @param alertId 告警ID
   */
  static async markAlertAsHandled(alertId: string): Promise<void> {
    try {
      await post(`/dashboard/alerts/${alertId}/handle`)
    } catch (error) {
      console.error('标记告警失败:', error)
      throw new Error(`标记告警失败: ${handleApiError(error)}`)
    }
  }

  /**
   * 获取历史趋势数据
   * @param params 查询参数
   */
  static async getTrendData(params?: StatsQueryParams): Promise<any> {
    try {
      return await get('/dashboard/trends', params)
    } catch (error) {
      console.error('获取趋势数据失败:', error)
      throw new Error(`获取趋势数据失败: ${handleApiError(error)}`)
    }
  }
}

/**
 * 导出便捷的API方法
 * 可以直接导入使用，无需通过类调用
 */
export const {
  getDashboardData,
  getKeyMetrics,
  getRealtimeAlerts,
  getRegionStats,
  getTransactionData,
  getSystemStatus,
  refreshAllData,
  getAlertDetail,
  getTransactionDetail,
  markAlertAsHandled,
  getTrendData
} = DashboardAPI

// 默认导出
export default DashboardAPI
