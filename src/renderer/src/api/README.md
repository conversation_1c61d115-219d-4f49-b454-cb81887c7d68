# 银行告警监控系统 - API接口文档

## 文件结构

```
src/renderer/src/api/
├── index.ts                    # API基础配置和通用请求方法
├── types.ts                    # TypeScript类型定义
├── dashboard.ts                # Dashboard相关API接口
├── dashboard-usage-example.ts  # 使用示例和Composable
└── README.md                   # 本文档
```

## 快速开始

### 1. 配置后端服务器地址

在 `api/index.ts` 中修改 `API_CONFIG.BASE_URL`：

```typescript
export const API_CONFIG = {
  BASE_URL: process.env.NODE_ENV === 'development' 
    ? 'http://localhost:3000/api'  // 你的开发环境后端地址
    : 'http://your-production-server.com/api', // 生产环境地址
}
```

### 2. 在Dashboard.vue中使用

```typescript
// 导入API方法
import { getDashboardData, getSystemStatus } from '@/api/dashboard'

// 或者使用Composable（推荐）
import { useDashboardData } from '@/api/dashboard-usage-example'

const {
  keyMetrics,
  realtimeAlerts,
  systemStatus,
  loadAllData,
  refreshData
} = useDashboardData()

// 在组件挂载时加载数据
onMounted(async () => {
  await loadAllData()
})
```

## API接口说明

### 后端需要实现的接口

| 接口路径 | 方法 | 说明 | 返回数据 |
|---------|------|------|----------|
| `/api/dashboard/summary` | GET | 获取仪表盘汇总数据 | DashboardData |
| `/api/dashboard/metrics` | GET | 获取关键指标 | KeyMetric[] |
| `/api/dashboard/alerts` | GET | 获取实时告警 | PaginatedResponse<AlertItem> |
| `/api/dashboard/region-stats` | GET | 获取地域分布统计 | RegionStat[] |
| `/api/dashboard/transactions` | GET | 获取交易监控数据 | PaginatedResponse<TransactionItem> |
| `/api/dashboard/system-status` | GET | 获取系统状态 | SystemStatus |
| `/api/dashboard/refresh` | POST | 刷新所有数据 | void |

### 数据类型说明

#### KeyMetric (关键指标)
```typescript
interface KeyMetric {
  id: string          // 指标ID
  title: string       // 指标标题
  value: string       // 指标值
  change: string      // 变化百分比
  trend: 'up' | 'down' | 'neutral'  // 趋势
  icon: string        // 图标名称
  iconColor: string   // 图标颜色
}
```

#### AlertItem (告警项)
```typescript
interface AlertItem {
  id: string          // 告警ID
  title: string       // 告警标题
  description: string // 告警描述
  level: 'critical' | 'warning' | 'info'  // 告警级别
  timestamp: string   // 时间戳 (ISO 8601)
  source?: string     // 告警来源
  category?: string   // 告警分类
}
```

#### SystemStatus (系统状态)
```typescript
interface SystemStatus {
  overall: 'normal' | 'warning' | 'critical'  // 整体状态
  cpu: SystemStatusItem      // CPU状态
  memory: SystemStatusItem   // 内存状态
  network: SystemStatusItem  // 网络状态
  database: SystemStatusItem // 数据库状态
  lastUpdate: string         // 最后更新时间
}
```

## 使用建议

### 1. 错误处理

```typescript
try {
  const data = await getDashboardData()
  // 处理成功数据
} catch (error) {
  console.error('获取数据失败:', error)
  // 显示错误提示给用户
}
```

### 2. 加载状态管理

```typescript
const isLoading = ref(false)

const loadData = async () => {
  isLoading.value = true
  try {
    const data = await getDashboardData()
    // 更新数据
  } finally {
    isLoading.value = false
  }
}
```

### 3. 自动刷新

```typescript
// 使用提供的Composable
const { startAutoRefresh, stopAutoRefresh } = useDashboardData()

onMounted(() => {
  startAutoRefresh(30000) // 30秒刷新一次
})

onUnmounted(() => {
  stopAutoRefresh()
})
```

## 后端接口示例

### 响应格式

所有接口都应该返回统一的响应格式：

```json
{
  "code": 200,
  "message": "success",
  "data": { /* 实际数据 */ },
  "timestamp": 1640995200000
}
```

### 示例接口实现 (Node.js/Express)

```javascript
// GET /api/dashboard/metrics
app.get('/api/dashboard/metrics', (req, res) => {
  const metrics = [
    {
      id: 'total-alerts',
      title: '今日告警总数',
      value: '127',
      change: '+23%',
      trend: 'up',
      icon: 'AlertTriangle',
      iconColor: 'text-destructive'
    },
    // ... 更多指标
  ]
  
  res.json({
    code: 200,
    message: 'success',
    data: metrics,
    timestamp: Date.now()
  })
})
```

## 注意事项

1. **认证**: 如果需要认证，在 `api/index.ts` 的 `addAuthHeaders` 函数中添加token逻辑
2. **环境变量**: 建议使用环境变量配置不同环境的API地址
3. **错误处理**: 所有API调用都应该包含适当的错误处理
4. **类型安全**: 充分利用TypeScript类型定义，确保数据类型正确
5. **性能优化**: 对于高频更新的数据（如系统状态），考虑使用WebSocket或Server-Sent Events

## 下一步

1. 根据你的后端技术栈实现对应的API接口
2. 在Dashboard.vue中替换现有的模拟数据
3. 测试API连接和数据展示
4. 根据实际需求调整数据结构和接口
