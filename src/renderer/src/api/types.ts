/**
 * API响应数据类型定义
 * 银行告警监控系统 - 数据接口类型
 */

// 基础响应接口
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp: number
}

// 关键指标数据类型
export interface KeyMetric {
  id: string
  title: string
  value: string
  change: string
  trend: 'up' | 'down' | 'neutral'
  icon: string
  iconColor: string
}

// 告警数据类型
export interface AlertItem {
  id: string
  title: string
  description: string
  level: 'critical' | 'warning' | 'info'
  timestamp: string // ISO 8601 格式
  source?: string // 告警来源
  category?: string // 告警分类
}

// 地域分布统计类型
export interface RegionStat {
  name: string
  count: number
  percentage: number
  color: string
  variant: 'default' | 'secondary' | 'destructive' | 'outline'
}

// 交易数据类型
export interface TransactionItem {
  id: string
  account: string
  amount: number
  type: string
  status: 'completed' | 'pending' | 'failed'
  riskLevel: 'high' | 'medium' | 'low'
  timestamp: string // ISO 8601 格式
  location?: string // 交易地点
  description?: string // 交易描述
}

// 系统状态类型
export interface SystemStatusItem {
  name: string
  value: string
  percentage: number
  status: 'normal' | 'warning' | 'critical'
  color: string
  unit?: string // 单位
  threshold?: number // 阈值
}

// 系统整体状态
export interface SystemStatus {
  overall: 'normal' | 'warning' | 'critical'
  cpu: SystemStatusItem
  memory: SystemStatusItem
  network: SystemStatusItem
  database: SystemStatusItem
  lastUpdate: string
}

// Dashboard汇总数据类型
export interface DashboardData {
  keyMetrics: KeyMetric[]
  realtimeAlerts: AlertItem[]
  regionStats: RegionStat[]
  transactionData: TransactionItem[]
  systemStatus: SystemStatus
}

// 分页参数
export interface PaginationParams {
  page: number
  pageSize: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// 分页响应
export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// 查询参数类型
export interface AlertQueryParams extends PaginationParams {
  level?: 'critical' | 'warning' | 'info'
  startTime?: string
  endTime?: string
  source?: string
}

export interface TransactionQueryParams extends PaginationParams {
  status?: 'completed' | 'pending' | 'failed'
  riskLevel?: 'high' | 'medium' | 'low'
  startTime?: string
  endTime?: string
  minAmount?: number
  maxAmount?: number
}

// 统计查询参数
export interface StatsQueryParams {
  startTime?: string
  endTime?: string
  groupBy?: 'hour' | 'day' | 'week' | 'month'
}
