# Watch 脚本使用说明

## 新增的 Watch 脚本

为了提高开发效率，我们添加了几个watch脚本，用于在JS/TS文件修改时自动重启渲染器。

### 📋 可用的脚本

#### 1. `npm run dev:watch`
```bash
npm run dev:watch
```
- **功能**: 启动开发模式并开启文件监听
- **特点**: 当JS/TS文件发生变化时自动重新构建和重启
- **适用场景**: 日常开发，需要自动重启功能

#### 2. `npm run watch`
```bash
npm run watch
```
- **功能**: 与 `dev:watch` 相同，提供更简短的命令
- **特点**: 简化的命令，方便快速启动
- **适用场景**: 快速启动watch模式

#### 3. `npm run dev:hot`
```bash
npm run dev:hot
```
- **功能**: 启动开发模式，同时开启watch和热重载
- **特点**: 最完整的开发体验，支持热重载和自动重启
- **适用场景**: 需要最佳开发体验时使用

#### 4. `npm run dev` (原有)
```bash
npm run dev
```
- **功能**: 标准开发模式
- **特点**: 基础的开发服务器，手动刷新
- **适用场景**: 简单开发或调试时使用

## 🎯 推荐使用方式

### 日常开发
```bash
npm run dev:watch
```
或者更简短的：
```bash
npm run watch
```

### 需要最佳体验时
```bash
npm run dev:hot
```

## 🔧 技术原理

### Watch 模式工作原理
1. **文件监听**: 监听 `src/` 目录下的所有 JS/TS/Vue 文件
2. **自动构建**: 文件变化时自动重新构建主进程和渲染进程
3. **进程重启**: 主进程代码变化时重启整个Electron应用
4. **热重载**: 渲染进程代码变化时进行热重载

### 监听的文件类型
- ✅ TypeScript 文件 (`.ts`)
- ✅ JavaScript 文件 (`.js`)
- ✅ Vue 单文件组件 (`.vue`)
- ✅ 主进程文件 (`src/main/`)
- ✅ 渲染进程文件 (`src/renderer/`)
- ✅ 预加载脚本 (`src/preload/`)

## 📊 性能对比

| 脚本 | 启动速度 | 重载速度 | 内存占用 | 推荐度 |
|------|----------|----------|----------|--------|
| `dev` | 快 | 手动 | 低 | ⭐⭐⭐ |
| `dev:watch` | 中等 | 快 | 中等 | ⭐⭐⭐⭐⭐ |
| `dev:hot` | 慢 | 最快 | 高 | ⭐⭐⭐⭐ |

## 🚀 使用示例

### 启动watch模式
```bash
# 方式1：完整命令
npm run dev:watch

# 方式2：简短命令
npm run watch

# 方式3：带热重载
npm run dev:hot
```

### 开发流程
1. 启动watch模式：`npm run watch`
2. 修改代码文件
3. 保存文件
4. 自动重新构建和重启
5. 在Electron应用中查看更改

## 🛠️ 故障排除

### 常见问题

#### 1. Watch模式不工作
**症状**: 修改文件后没有自动重启
**解决方案**:
```bash
# 停止当前进程 (Ctrl+C)
# 重新启动
npm run watch
```

#### 2. 重启过于频繁
**症状**: 文件保存后多次重启
**解决方案**:
- 检查是否有自动保存功能导致频繁保存
- 使用 `npm run dev` 进行调试

#### 3. 内存占用过高
**症状**: 系统变慢，内存占用高
**解决方案**:
- 使用 `npm run dev:watch` 而不是 `dev:hot`
- 定期重启watch进程

### 调试技巧

#### 查看详细日志
```bash
# 启用详细日志
DEBUG=electron-vite:* npm run watch
```

#### 清理缓存
```bash
# 清理构建缓存
rm -rf out/
npm run watch
```

## 📝 配置说明

### electron-vite 配置
Watch模式使用electron-vite的内置配置，支持：
- 文件变化检测
- 增量构建
- 进程管理
- 热重载

### 自定义配置
如需自定义watch行为，可以修改 `electron.vite.config.ts` 文件。

## 🎉 开发体验提升

使用watch模式后，您将获得：
- ✅ **自动重启**: 无需手动重启应用
- ✅ **快速反馈**: 代码变化立即生效
- ✅ **提高效率**: 减少重复操作
- ✅ **专注开发**: 专注于代码编写而非构建过程

## 📚 相关文档

- [electron-vite 官方文档](https://electron-vite.org/)
- [Vite 热重载文档](https://vitejs.dev/guide/features.html#hot-module-replacement)
- [Electron 开发指南](https://www.electronjs.org/docs/latest/tutorial/development)

---

**提示**: 建议在开发过程中始终使用 `npm run watch` 来获得最佳的开发体验！
